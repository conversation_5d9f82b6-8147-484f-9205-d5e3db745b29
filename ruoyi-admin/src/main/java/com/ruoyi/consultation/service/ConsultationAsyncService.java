package com.ruoyi.consultation.service;

import com.ruoyi.consultation.domain.ConsultationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 会诊异步服务
 * 处理会诊相关的异步操作，提升主流程性能
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@Slf4j
public class ConsultationAsyncService {

    @Autowired
    private NotificationSendService notificationSendService;

    @Autowired
    private ConsultationAuditService auditService;


    /**
     * 异步发送会诊完成通知
     *
     * @param consultation 会诊申请
     */
    @Async("consultationNotificationExecutor")
    public void sendConsultationCompletedNotificationAsync(ConsultationRequest consultation) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始异步发送会诊完成通知，申请编号: {}", consultation.getRequestNo());
            
            Long notificationId = notificationSendService.sendConsultationCompletedNotification(consultation);
            boolean success = notificationId != null;
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("异步发送会诊完成通知{}，申请编号: {}，耗时: {}ms", 
                success ? "成功" : "失败", consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步发送会诊完成通知异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步发送会诊接受通知
     *
     * @param consultation 会诊申请
     */
    @Async("consultationNotificationExecutor")
    public void sendConsultationAcceptedNotificationAsync(ConsultationRequest consultation) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始异步发送会诊接受通知，申请编号: {}", consultation.getRequestNo());
            
            Long notificationId = notificationSendService.sendConsultationAcceptedNotification(consultation);
            boolean success = notificationId != null;
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("异步发送会诊接受通知{}，申请编号: {}，耗时: {}ms", 
                success ? "成功" : "失败", consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步发送会诊接受通知异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步发送会诊拒绝通知
     *
     * @param consultation 会诊申请
     * @param rejectReason 拒绝原因
     */
    @Async("consultationNotificationExecutor")
    public void sendConsultationRejectedNotificationAsync(ConsultationRequest consultation, String rejectReason) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始异步发送会诊拒绝通知，申请编号: {}", consultation.getRequestNo());
            
            Long notificationId = notificationSendService.sendConsultationRejectedNotification(consultation, rejectReason);
            boolean success = notificationId != null;
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("异步发送会诊拒绝通知{}，申请编号: {}，耗时: {}ms", 
                success ? "成功" : "失败", consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步发送会诊拒绝通知异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步记录创建会诊审计日志
     *
     * @param consultation 会诊申请
     */
    @Async("consultationAuditExecutor")
    public void logCreateConsultationAsync(ConsultationRequest consultation) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("开始异步记录创建会诊审计日志，申请编号: {}", consultation.getRequestNo());
            
            auditService.logCreateConsultation(consultation);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("异步记录创建会诊审计日志完成，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步记录创建会诊审计日志异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步记录完成会诊审计日志
     *
     * @param consultation 会诊申请
     */
    @Async("consultationAuditExecutor")
    public void logCompleteConsultationAsync(ConsultationRequest consultation) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("开始异步记录完成会诊审计日志，申请编号: {}", consultation.getRequestNo());
            
            auditService.logCompleteConsultation(consultation);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("异步记录完成会诊审计日志完成，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步记录完成会诊审计日志异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步记录接受会诊审计日志
     *
     * @param consultation 会诊申请
     * @param acceptReason 接受原因
     */
    @Async("consultationAuditExecutor")
    public void logAcceptConsultationAsync(ConsultationRequest consultation, String acceptReason) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("开始异步记录接受会诊审计日志，申请编号: {}", consultation.getRequestNo());
            
            auditService.logAcceptConsultation(consultation, acceptReason);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("异步记录接受会诊审计日志完成，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步记录接受会诊审计日志异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步记录拒绝会诊审计日志
     *
     * @param consultation 会诊申请
     * @param rejectReason 拒绝原因
     */
    @Async("consultationAuditExecutor")
    public void logRejectConsultationAsync(ConsultationRequest consultation, String rejectReason) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("开始异步记录拒绝会诊审计日志，申请编号: {}", consultation.getRequestNo());
            
            auditService.logRejectConsultation(consultation, rejectReason);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("异步记录拒绝会诊审计日志完成，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步记录拒绝会诊审计日志异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }

    /**
     * 异步记录取消会诊审计日志
     *
     * @param consultation 会诊申请
     * @param cancelReason 取消原因
     */
    @Async("consultationAuditExecutor")
    public void logCancelConsultationAsync(ConsultationRequest consultation, String cancelReason) {
        long startTime = System.currentTimeMillis();
        try {
            log.debug("开始异步记录取消会诊审计日志，申请编号: {}", consultation.getRequestNo());
            
            auditService.logCancelConsultation(consultation, cancelReason);
            
            long duration = System.currentTimeMillis() - startTime;
            log.debug("异步记录取消会诊审计日志完成，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration);
                
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("异步记录取消会诊审计日志异常，申请编号: {}，耗时: {}ms", 
                consultation.getRequestNo(), duration, e);
        }
    }
}
