package com.ruoyi.consultation.service;

import com.alibaba.fastjson2.JSON;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.sms.AliSmsUtils;
import com.ruoyi.consultation.domain.ConsultationNotification;
import com.ruoyi.consultation.domain.ConsultationRequest;
import com.ruoyi.consultation.mapper.ConsultationNotificationMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.web.controller.consultation.ConsultationNotificationController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会诊通知服务
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@Slf4j
public class ConsultationNotificationService implements IConsultationNotificationService {

    @Autowired
    private AliSmsUtils aliSmsUtils;

    @Autowired
    private ConsultationNotificationMapper notificationMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 发送会诊申请通知
     */
    @Override
    public boolean sendConsultationRequestNotification(ConsultationRequest consultation) {
        log.info("发送会诊申请通知，申请编号: {}", consultation.getRequestNo());

        // 构建短信内容
        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("requesterName", consultation.getRequesterName());
        templateParam.put("patientName", consultation.getPatientStudy() != null ?
            consultation.getPatientStudy().getPatientName() : "未知患者");
        templateParam.put("urgency", getUrgencyText(consultation.getUrgencyLevel()));
        templateParam.put("requestNo", consultation.getRequestNo());

        String templateCode = configService.selectConfigByKey("consultation.sms.template.request");
        if (StringUtils.isEmpty(templateCode)) {
            templateCode = "SMS_CONSULTATION_REQUEST";
        }

        // 发送短信通知
        boolean smsResult = sendNotification(consultation, "CONSULTATION_REQUEST", templateCode,
            consultation.getConsultantId(), consultation.getConsultantPhone(), templateParam);

        // 发送站内通知
        sendInAppNotification(consultation, "REQUEST");

        return smsResult;
    }
    
    /**
     * 发送会诊完成通知
     */
    @Override
    public boolean sendConsultationCompletedNotification(ConsultationRequest consultation) {
        log.info("发送会诊完成通知，申请编号: {}", consultation.getRequestNo());

        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("consultantName", consultation.getConsultantName());
        templateParam.put("patientName", consultation.getPatientStudy() != null ?
            consultation.getPatientStudy().getPatientName() : "未知患者");
        templateParam.put("requestNo", consultation.getRequestNo());

        String templateCode = configService.selectConfigByKey("consultation.sms.template.completed");
        if (StringUtils.isEmpty(templateCode)) {
            templateCode = "SMS_CONSULTATION_COMPLETED";
        }

        // 发送短信通知
        boolean smsResult = sendNotification(consultation, "CONSULTATION_COMPLETED", templateCode,
            consultation.getRequesterId(), getRequesterPhone(consultation), templateParam);

        // 发送站内通知
        sendInAppNotification(consultation, "COMPLETE");

        return smsResult;
    }

    /**
     * 发送会诊接受通知
     */
    @Override
    public boolean sendConsultationAcceptedNotification(ConsultationRequest consultation) {
        log.info("发送会诊接受通知，申请编号: {}", consultation.getRequestNo());

        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("consultantName", consultation.getConsultantName());
        templateParam.put("patientName", consultation.getPatientStudy() != null ?
            consultation.getPatientStudy().getPatientName() : "未知患者");
        templateParam.put("requestNo", consultation.getRequestNo());

        String templateCode = configService.selectConfigByKey("consultation.sms.template.accepted");
        if (StringUtils.isEmpty(templateCode)) {
            templateCode = "SMS_CONSULTATION_ACCEPTED";
        }

        // 发送短信通知
        boolean smsResult = sendNotification(consultation, "CONSULTATION_ACCEPTED", templateCode,
            consultation.getRequesterId(), getRequesterPhone(consultation), templateParam);

        // 发送站内通知
        sendInAppNotification(consultation, "ACCEPT");

        return smsResult;
    }

    /**
     * 发送会诊拒绝通知
     */
    @Override
    public boolean sendConsultationRejectedNotification(ConsultationRequest consultation, String rejectReason) {
        log.info("发送会诊拒绝通知，申请编号: {}", consultation.getRequestNo());
        
        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("consultantName", consultation.getConsultantName());
        templateParam.put("patientName", consultation.getPatientStudy() != null ? 
            consultation.getPatientStudy().getPatientName() : "未知患者");
        templateParam.put("requestNo", consultation.getRequestNo());
        templateParam.put("reason", rejectReason != null ? rejectReason : "未说明原因");
        
        String templateCode = configService.selectConfigByKey("consultation.sms.template.rejected");
        if (StringUtils.isEmpty(templateCode)) {
            templateCode = "SMS_CONSULTATION_REJECTED";
        }
        
        // 发送短信通知
        boolean smsResult = sendNotification(consultation, "CONSULTATION_REJECTED", templateCode,
            consultation.getRequesterId(), getRequesterPhone(consultation), templateParam);

        // 发送站内通知
        sendInAppNotification(consultation, "REJECT");

        return smsResult;
    }
    
    /**
     * 发送通知的通用方法
     */
    private boolean sendNotification(ConsultationRequest consultation, String notificationType, 
            String templateCode, Long recipientId, String phoneNumber, Map<String, String> templateParam) {
        
        // 记录通知日志
        ConsultationNotification notification = new ConsultationNotification();
        notification.setConsultationId(consultation.getId());
        notification.setNotificationType("SMS");
        notification.setRecipientId(recipientId);
        notification.setRecipientPhone(phoneNumber);
        notification.setTemplateCode(templateCode);
        notification.setNotificationContent(JSON.toJSONString(templateParam));
        notification.setCreateTime(new Date());
        
        if (StringUtils.isEmpty(phoneNumber) || StringUtils.isEmpty(templateCode)) {
            log.warn("发送通知失败，手机号或模板编码为空，申请编号: {}", consultation.getRequestNo());
            notification.setSendStatus("FAILED");
            notification.setErrorMessage("手机号或模板编码为空");
            notificationMapper.insertConsultationNotification(notification);
            return false;
        }
        
        try {
            SendSmsResponse response = aliSmsUtils.sendSms(phoneNumber, templateCode, templateParam);
            if (aliSmsUtils.isSuccess(response)) {
                notification.setSendStatus("SUCCESS");
                notification.setSendTime(new Date());
                log.info("会诊通知发送成功，类型: {}, 手机号: {}, 申请编号: {}", 
                    notificationType, phoneNumber, consultation.getRequestNo());
            } else {
                notification.setSendStatus("FAILED");
                notification.setErrorMessage(response.getBody() != null ? 
                    response.getBody().getMessage() : "未知错误");
                log.error("会诊通知发送失败，类型: {}, 手机号: {}, 申请编号: {}, 错误: {}", 
                    notificationType, phoneNumber, consultation.getRequestNo(), 
                    notification.getErrorMessage());
            }
        } catch (Exception e) {
            notification.setSendStatus("FAILED");
            notification.setErrorMessage(e.getMessage());
            log.error("会诊通知发送异常，类型: {}, 手机号: {}, 申请编号: {}", 
                notificationType, phoneNumber, consultation.getRequestNo(), e);
        }
        
        notificationMapper.insertConsultationNotification(notification);
        return "SUCCESS".equals(notification.getSendStatus());
    }

    /**
     * 重试发送失败的通知
     */
    @Override
    public int retryFailedNotifications() {
        int maxRetryCount = 3;
        List<ConsultationNotification> failedNotifications = 
            notificationMapper.selectFailedNotifications(maxRetryCount);
        
        int successCount = 0;
        for (ConsultationNotification notification : failedNotifications) {
            try {
                Map<String, String> templateParam = JSON.parseObject(
                    notification.getNotificationContent(), Map.class);
                
                SendSmsResponse response = aliSmsUtils.sendSms(
                    notification.getRecipientPhone(), 
                    notification.getTemplateCode(), 
                    templateParam);
                
                notificationMapper.incrementRetryCount(notification.getId());
                
                if (aliSmsUtils.isSuccess(response)) {
                    notificationMapper.updateNotificationStatus(
                        notification.getId(), "SUCCESS", null);
                    successCount++;
                } else {
                    notificationMapper.updateNotificationStatus(
                        notification.getId(), "FAILED", 
                        response.getBody() != null ? response.getBody().getMessage() : "重试失败");
                }
            } catch (Exception e) {
                notificationMapper.incrementRetryCount(notification.getId());
                notificationMapper.updateNotificationStatus(
                    notification.getId(), "FAILED", e.getMessage());
                log.error("重试发送通知异常，通知ID: {}", notification.getId(), e);
            }
        }
        
        log.info("重试发送失败通知完成，总数: {}, 成功: {}", failedNotifications.size(), successCount);
        return successCount;
    }

    /**
     * 获取紧急程度文本
     */
    private String getUrgencyText(String urgencyLevel) {
        switch (urgencyLevel) {
            case "URGENT":
                return "紧急";
            case "NORMAL":
                return "普通";
            case "LOW":
                return "非紧急";
            default:
                return "普通";
        }
    }

    /**
     * 获取申请人手机号
     */
    private String getRequesterPhone(ConsultationRequest consultation) {
        // 这里需要根据申请人ID查询手机号
        // 可以通过用户服务获取
        if (consultation.getRequester() != null) {
            return consultation.getRequester().getPhonenumber();
        }
        return null;
    }

    /**
     * 发送站内通知
     */
    private void sendInAppNotification(ConsultationRequest consultation, String notificationType) {
        try {
            ConsultationNotificationController.ConsultationNotificationMessage message =
                new ConsultationNotificationController.ConsultationNotificationMessage();

            message.setType(notificationType);
            message.setRequestNo(consultation.getRequestNo());
            message.setConsultationId(consultation.getId());
            message.setRequesterName(consultation.getRequesterName());
            message.setPatientName(consultation.getPatientStudy() != null ?
                consultation.getPatientStudy().getPatientName() : "未知患者");
            message.setUrgencyLevel(consultation.getUrgencyLevel());

            Long recipientId = null;
            switch (notificationType) {
                case "REQUEST":
                    // 会诊申请通知发送给会诊医生
                    recipientId = consultation.getConsultantId();
                    message.setTitle("新会诊申请");
                    message.setContent(String.format("您收到来自 %s 的会诊申请，患者：%s，申请编号：%s",
                        consultation.getRequesterName(),
                        message.getPatientName(),
                        consultation.getRequestNo()));
                    break;
                case "ACCEPT":
                    // 会诊接受通知发送给申请人
                    recipientId = consultation.getRequesterId();
                    message.setTitle("会诊申请已接受");
                    message.setContent(String.format("您的会诊申请已被 %s 接受，申请编号：%s",
                        consultation.getConsultantName(),
                        consultation.getRequestNo()));
                    break;
                case "REJECT":
                    // 会诊拒绝通知发送给申请人
                    recipientId = consultation.getRequesterId();
                    message.setTitle("会诊申请已拒绝");
                    message.setContent(String.format("您的会诊申请已被 %s 拒绝，申请编号：%s",
                        consultation.getConsultantName(),
                        consultation.getRequestNo()));
                    break;
                case "COMPLETE":
                    // 会诊完成通知发送给申请人
                    recipientId = consultation.getRequesterId();
                    message.setTitle("会诊已完成");
                    message.setContent(String.format("您的会诊申请已完成，会诊医生：%s，申请编号：%s",
                        consultation.getConsultantName(),
                        consultation.getRequestNo()));
                    break;
            }

            if (recipientId != null) {
                boolean success = ConsultationNotificationController.sendNotificationToUser(recipientId, message);
                log.info("站内通知发送{}，类型：{}，接收人ID：{}，申请编号：{}",
                    success ? "成功" : "失败", notificationType, recipientId, consultation.getRequestNo());
            }

        } catch (Exception e) {
            log.error("发送站内通知失败，申请编号：{}", consultation.getRequestNo(), e);
        }
    }

    /**
     * 查询会诊通知
     *
     * @param id 会诊通知主键
     * @return 会诊通知
     */
    @Override
    public ConsultationNotification selectConsultationNotificationById(Long id) {
        return notificationMapper.selectConsultationNotificationById(id);
    }

    /**
     * 查询会诊通知列表
     *
     * @param consultationNotification 会诊通知
     * @return 会诊通知集合
     */
    @Override
    public List<ConsultationNotification> selectConsultationNotificationList(ConsultationNotification consultationNotification) {
        return notificationMapper.selectConsultationNotificationList(consultationNotification);
    }

    /**
     * 新增会诊通知
     *
     * @param consultationNotification 会诊通知
     * @return 结果
     */
    @Override
    public int insertConsultationNotification(ConsultationNotification consultationNotification) {
        return notificationMapper.insertConsultationNotification(consultationNotification);
    }

    /**
     * 修改会诊通知
     *
     * @param consultationNotification 会诊通知
     * @return 结果
     */
    @Override
    public int updateConsultationNotification(ConsultationNotification consultationNotification) {
        return notificationMapper.updateConsultationNotification(consultationNotification);
    }

    /**
     * 批量删除会诊通知
     *
     * @param ids 需要删除的会诊通知主键
     * @return 结果
     */
    @Override
    public int deleteConsultationNotificationByIds(Long[] ids) {
        return notificationMapper.deleteConsultationNotificationByIds(ids);
    }

    /**
     * 删除会诊通知信息
     *
     * @param id 会诊通知主键
     * @return 结果
     */
    @Override
    public int deleteConsultationNotificationById(Long id) {
        return notificationMapper.deleteConsultationNotificationById(id);
    }



    /**
     * 发送会诊申请通知
     *
     * @param consultation 会诊申请
     * @return 通知ID
     */
    public Long sendConsultationRequestNotification(ConsultationRequest consultation) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createRequestNotification(
                            consultation.getRequestNo(),
                            consultation.getRequesterName(),
                            consultation.getConsultantName(),
                            consultation.getPatientName(),
                            consultation.getUrgencyLevel()
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getConsultantId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送会诊申请通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送会诊接受通知
     *
     * @param consultation 会诊申请
     * @return 通知ID
     */
    public Long sendConsultationAcceptedNotification(ConsultationRequest consultation) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createAcceptNotification(
                            consultation.getRequestNo(),
                            consultation.getConsultantName(),
                            consultation.getRequesterName(),
                            consultation.getPatientName()
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getRequesterId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送会诊接受通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送会诊拒绝通知
     *
     * @param consultation 会诊申请
     * @param rejectReason 拒绝原因
     * @return 通知ID
     */
    public Long sendConsultationRejectedNotification(ConsultationRequest consultation, String rejectReason) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createRejectNotification(
                            consultation.getRequestNo(),
                            consultation.getConsultantName(),
                            consultation.getRequesterName(),
                            consultation.getPatientName(),
                            rejectReason
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getRequesterId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送会诊拒绝通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送会诊完成通知
     *
     * @param consultation 会诊申请
     * @return 通知ID
     */
    public Long sendConsultationCompletedNotification(ConsultationRequest consultation) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createCompleteNotification(
                            consultation.getRequestNo(),
                            consultation.getConsultantName(),
                            consultation.getRequesterName(),
                            consultation.getPatientName()
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getRequesterId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送会诊完成通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送会诊取消通知
     *
     * @param consultation 会诊申请
     * @param cancelReason 取消原因
     * @return 通知ID
     */
    public Long sendConsultationCancelledNotification(ConsultationRequest consultation, String cancelReason) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createCancelNotification(
                            consultation.getRequestNo(),
                            consultation.getRequesterName(),
                            consultation.getConsultantName(),
                            consultation.getPatientName(),
                            cancelReason
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getConsultantId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送会诊取消通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送紧急会诊通知
     *
     * @param consultation 会诊申请
     * @return 通知ID
     */
    public Long sendUrgentConsultationNotification(ConsultationRequest consultation) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createUrgentNotification(
                            consultation.getRequestNo(),
                            consultation.getRequesterName(),
                            consultation.getConsultantName(),
                            consultation.getPatientName()
                    );

            return reliableNotificationService.sendReliableNotification(
                    consultation.getConsultantId(), message, consultation.getId());

        } catch (Exception e) {
            log.error("发送紧急会诊通知失败，申请编号: {}", consultation.getRequestNo(), e);
            return null;
        }
    }

    /**
     * 发送系统通知
     *
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param url 跳转URL
     * @return 通知ID
     */
    public Long sendSystemNotification(Long userId, String title, String content, String url) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createSystemNotification(title, content, url);

            return reliableNotificationService.sendReliableNotification(userId, message, null);

        } catch (Exception e) {
            log.error("发送系统通知失败，用户ID: {}, 标题: {}", userId, title, e);
            return null;
        }
    }

    /**
     * 批量发送通知
     *
     * @param userIds 用户ID列表
     * @param message 通知消息
     * @return 成功发送的数量
     */
    public int batchSendNotification(List<Long> userIds, ConsultationNotificationMessage message) {
        int successCount = 0;

        for (Long userId : userIds) {
            try {
                Long notificationId = reliableNotificationService.sendReliableNotification(userId, message, null);
                if (notificationId != null) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量发送通知失败，用户ID: {}", userId, e);
            }
        }

        log.info("批量发送通知完成，目标用户: {}, 成功发送: {}", userIds.size(), successCount);
        return successCount;
    }

    /**
     * 发送广播通知
     *
     * @param title 标题
     * @param content 内容
     * @param url 跳转URL
     * @return 成功发送的用户数量
     */
    public int sendBroadcastNotification(String title, String content, String url) {
        try {
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                    .createSystemNotification(title, content, url);

            // 获取所有在线用户
            java.util.Set<Long> onlineUserIds = reliableNotificationService.getOnlineUserIds();

            if (onlineUserIds.isEmpty()) {
                log.warn("没有在线用户，广播通知取消");
                return 0;
            }

            return batchSendNotification(new java.util.ArrayList<>(onlineUserIds), message);

        } catch (Exception e) {
            log.error("发送广播通知失败，标题: {}", title, e);
            return 0;
        }
    }

    /**
     * 发送自定义通知
     *
     * @param userId 用户ID
     * @param message 自定义消息
     * @param consultationId 会诊ID（可选）
     * @return 通知ID
     */
    public Long sendCustomNotification(Long userId, ConsultationNotificationMessage message, Long consultationId) {
        try {
            return reliableNotificationService.sendReliableNotification(userId, message, consultationId);
        } catch (Exception e) {
            log.error("发送自定义通知失败，用户ID: {}, 消息类型: {}", userId, message.getType(), e);
            return null;
        }
    }

    /**
     * 重发失败的通知
     *
     * @param notificationId 通知ID
     * @return 是否重发成功
     */
    public boolean resendNotification(Long notificationId) {
        try {
            return resendNotification(notificationId);
        } catch (Exception e) {
            log.error("重发通知失败，通知ID: {}", notificationId, e);
            return false;
        }
    }
}
