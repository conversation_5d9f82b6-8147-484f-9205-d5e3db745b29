package com.ruoyi.web.controller.consultation;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.consultation.domain.PersistentNotification;
import com.ruoyi.consultation.message.ConsultationNotificationMessage;
import com.ruoyi.consultation.service.NotificationSendService;
import com.ruoyi.consultation.service.SseConnectionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 可靠通知控制器
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RestController
@RequestMapping("/consultation/notification")
@Slf4j
public class NotificationController extends BaseController {

    @Autowired
    private NotificationSendService notificationSendService;

    @Autowired
    private SseConnectionService sseConnectionService;

    /**
     * 建立SSE连接
     */
    @GetMapping(value = "/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 设置响应头
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Connection", "keep-alive");
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");

            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                log.error("用户未登录，无法建立SSE连接");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return null;
            }

            log.info("用户 {} 请求建立SSE连接", userId);

            // 通过Service创建连接
            SseEmitter emitter = sseConnectionService.createConnection(userId);

            // 处理用户上线
            notificationSendService.handleUserOnline(userId);

            return emitter;

        } catch (Exception e) {
            log.error("建立SSE连接失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return null;
        }
    }

    /**
     * 处理心跳响应
     */
    @PostMapping("/heartbeat")
    public AjaxResult heartbeat() {
        try {
            Long userId = SecurityUtils.getUserId();
            sseConnectionService.handleHeartbeatResponse(userId);

            log.debug("处理用户 {} 的心跳响应", userId);
            return success("心跳响应处理成功");

        } catch (Exception e) {
            log.error("处理心跳响应失败", e);
            return error("处理心跳响应失败: " + e.getMessage());
        }
    }

    /**
     * 确认通知已收到
     */
    @PostMapping("/acknowledge/{notificationId}")
    @Log(title = "确认通知", businessType = BusinessType.UPDATE)
    public AjaxResult acknowledgeNotification(@PathVariable Long notificationId) {
        Long userId = SecurityUtils.getUserId();
        boolean success = notificationSendService.acknowledgeNotification(notificationId, userId);

        if (success) {
            return success();
        } else {
            return error("确认通知失败");
        }
    }

    /**
     * 获取离线通知列表
     */
    @GetMapping("/offline-notifications")
    public AjaxResult getOfflineNotifications() {
        try {
            Long userId = SecurityUtils.getUserId();
            var notifications = notificationSendService.getOfflineNotifications(userId);

            log.info("用户 {} 查询离线通知，数量: {}", userId, notifications.size());
            return success(notifications);

        } catch (Exception e) {
            log.error("获取离线通知失败", e);
            return error("获取离线通知失败: " + e.getMessage());
        }
    }

    /**
     * 获取离线通知（兼容旧接口）
     */
    @GetMapping("/offline")
    public AjaxResult getOfflineNotificationsLegacy() {
        Long userId = SecurityUtils.getUserId();
        // 这个方法会在用户上线时自动调用，这里提供手动获取接口
        return success("离线通知将在连接建立时自动推送");
    }

    /**
     * 获取通知列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PersistentNotification notification) {
        startPage();
        Long userId = SecurityUtils.getUserId();
        notification.setUserId(userId);
        List<PersistentNotification> list = notificationSendService.getNotificationList(notification);
        return getDataTable(list);
    }

    /**
     * 标记通知为已读
     */
    @PostMapping("/mark-read/{notificationId}")
    @Log(title = "标记通知已读", businessType = BusinessType.UPDATE)
    public AjaxResult markAsRead(@PathVariable Long notificationId) {
        Long userId = SecurityUtils.getUserId();
        boolean success = notificationSendService.acknowledgeNotification(notificationId, userId);
        return success ? success() : error("标记失败");
    }

    /**
     * 批量标记通知为已读
     */
    @PostMapping("/batch-mark-read")
    @Log(title = "批量标记通知已读", businessType = BusinessType.UPDATE)
    public AjaxResult batchMarkAsRead(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> notificationIds = (List<Long>) params.get("notificationIds");
        Long userId = SecurityUtils.getUserId();
        
        int successCount = 0;
        for (Long notificationId : notificationIds) {
            if (notificationSendService.acknowledgeNotification(notificationId, userId)) {
                successCount++;
            }
        }
        
        return success("成功标记 " + successCount + " 条通知为已读");
    }

    /**
     * 获取通知统计信息
     */
    @GetMapping("/notification-statistics")
    public AjaxResult getNotificationStatistics() {
        Long userId = SecurityUtils.getUserId();
        Map<String, Object> statistics = notificationSendService.getNotificationStatistics(userId);
        return success(statistics);
    }

    /**
     * 检查用户在线状态
     */
    @GetMapping("/online-status/{userId}")
    public AjaxResult checkOnlineStatus(@PathVariable Long userId) {
        boolean isOnline = sseConnectionService.isUserOnline(userId);
        return success(Map.of("userId", userId, "isOnline", isOnline));
    }

    /**
     * 测试通知发送
     */
    @PostMapping("/test")
    @Log(title = "测试通知发送", businessType = BusinessType.OTHER)
    public AjaxResult testNotification(@RequestBody Map<String, Object> testData) {
        try {
            Long userId = SecurityUtils.getUserId();
            String title = (String) testData.getOrDefault("title", "测试通知");
            String content = (String) testData.getOrDefault("content", "这是一条测试通知");
            
            // 创建测试通知消息
            ConsultationNotificationMessage message = new ConsultationNotificationMessage();
            message.setType("TEST");
            message.setTitle(title);
            message.setContent(content);
            message.setUrl("/consultation/test");
            
            Long notificationId = notificationSendService.sendConsultationRequestNotification(userId, message, null);
            
            if (notificationId != null) {
                return success("测试通知发送成功，通知ID: " + notificationId);
            } else {
                return error("测试通知发送失败");
            }
            
        } catch (Exception e) {
            log.error("测试通知发送异常", e);
            return error("测试通知发送异常: " + e.getMessage());
        }
    }

    /**
     * 手动触发离线通知处理
     */
    @PostMapping("/process-offline")
    @Log(title = "处理离线通知", businessType = BusinessType.OTHER)
    public AjaxResult processOfflineNotifications() {
        Long userId = SecurityUtils.getUserId();
        notificationSendService.handleUserOnline(userId);
        return success("离线通知处理已触发");
    }

    /**
     * 重发通知
     */
    @PostMapping("/resend/{notificationId}")
    @Log(title = "重发通知", businessType = BusinessType.UPDATE)
    public AjaxResult resendNotification(@PathVariable Long notificationId) {
        try {
            boolean result = notificationSendService.resendNotification(notificationId);

            if (result) {
                log.info("重发通知 {} 成功", notificationId);
                return success("重发通知成功");
            } else {
                log.warn("重发通知 {} 失败", notificationId);
                return error("重发通知失败");
            }

        } catch (Exception e) {
            log.error("重发通知失败，通知ID: {}", notificationId, e);
            return error("重发通知失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期通知
     */
    @PostMapping("/cleanup")
    @Log(title = "清理过期通知", businessType = BusinessType.DELETE)
    public AjaxResult cleanupExpiredNotifications(@RequestBody Map<String, Object> params) {
        Integer retentionDays = (Integer) params.getOrDefault("retentionDays", 30);
        notificationSendService.cleanupExpiredNotifications(retentionDays);
        return success("过期通知清理任务已启动");
    }



    /**
     * 获取在线用户数量
     */
    @GetMapping("/online-count")
    public AjaxResult getOnlineCount() {
        return success(Map.of("onlineCount", sseConnectionService.getOnlineUserCount()));
    }

    /**
     * 获取在线用户列表
     */
    @GetMapping("/online-users")
    public AjaxResult getOnlineUsers() {
        return success(Map.of("onlineUsers", sseConnectionService.getOnlineUserIds()));
    }

    /**
     * 获取连接统计信息
     */
    @GetMapping("/connection-statistics")
    public AjaxResult getConnectionStatistics() {
        try {
            Map<String, Object> stats = sseConnectionService.getConnectionStatistics();
            return success(stats);

        } catch (Exception e) {
            log.error("获取连接统计信息失败", e);
            return error("获取连接统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接健康状态
     */
    @GetMapping("/health")
    public AjaxResult getConnectionHealth() {
        try {
            Map<String, Object> health = sseConnectionService.getConnectionHealth();
            return success(health);

        } catch (Exception e) {
            log.error("获取连接健康状态失败", e);
            return error("获取连接健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接统计信息（兼容旧接口）
     */
    @GetMapping("/connection-stats")
    public AjaxResult getConnectionStatisticsLegacy() {
        return success(sseConnectionService.getConnectionStatistics());
    }

    /**
     * 向指定用户发送测试通知
     */
    @PostMapping("/test-send/{userId}")
    @Log(title = "发送测试通知", businessType = BusinessType.OTHER)
    public AjaxResult sendTestNotification(@PathVariable Long userId, @RequestBody Map<String, String> message) {
        try {
            String content = message.get("content");
            boolean result = sseConnectionService.sendCustomEvent(userId, "test-notification", content);

            if (result) {
                log.info("向用户 {} 发送测试通知成功", userId);
                return success("测试通知发送成功");
            } else {
                log.warn("向用户 {} 发送测试通知失败，用户可能离线", userId);
                return error("测试通知发送失败，用户可能离线");
            }

        } catch (Exception e) {
            log.error("发送测试通知失败，用户ID: {}", userId, e);
            return error("发送测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 广播测试通知给所有在线用户
     */
    @PostMapping("/broadcast-test")
    @Log(title = "广播测试通知", businessType = BusinessType.OTHER)
    public AjaxResult broadcastTestNotification(@RequestBody Map<String, String> message) {
        try {
            String content = message.get("content");
            int count = sseConnectionService.broadcastNotification(content);

            log.info("广播测试通知完成，成功发送给 {} 个用户", count);
            return success("广播测试通知完成，成功发送给 " + count + " 个用户");

        } catch (Exception e) {
            log.error("广播测试通知失败", e);
            return error("广播测试通知失败: " + e.getMessage());
        }
    }

    /**
     * 强制断开指定用户的连接
     */
    @PostMapping("/disconnect/{userId}")
    @Log(title = "断开用户连接", businessType = BusinessType.OTHER)
    public AjaxResult disconnectUser(@PathVariable Long userId) {
        try {
            sseConnectionService.removeConnection(userId);
            log.info("强制断开用户 {} 的连接", userId);
            return success("用户连接已断开");

        } catch (Exception e) {
            log.error("断开用户连接失败，用户ID: {}", userId, e);
            return error("断开用户连接失败: " + e.getMessage());
        }
    }

    /**
     * 发送自定义通知
     */
    @PostMapping("/send-custom")
    @Log(title = "发送自定义通知", businessType = BusinessType.OTHER)
    public AjaxResult sendCustomNotification(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String title = (String) params.get("title");
            String content = (String) params.get("content");
            String type = (String) params.get("type");

            // 创建通知消息
            ConsultationNotificationMessage message = ConsultationNotificationMessage
                .createSystemNotification(title, content, null);

            // 使用可靠通知服务发送
            Long notificationId = notificationSendService.sendConsultationRequestNotification(userId, message, null);

            if (notificationId != null) {
                log.info("向用户 {} 发送自定义通知成功，通知ID: {}", userId, notificationId);
                return success("自定义通知发送成功，通知ID: " + notificationId);
            } else {
                log.warn("向用户 {} 发送自定义通知失败", userId);
                return error("自定义通知发送失败");
            }

        } catch (Exception e) {
            log.error("发送自定义通知失败", e);
            return error("发送自定义通知失败: " + e.getMessage());
        }
    }

    /**
     * 调试：检查SSE连接状态
     */
    @GetMapping("/debug-connections")
    public AjaxResult debugConnections() {
        try {
            Long userId = SecurityUtils.getUserId();

            // 检查当前用户的SSE连接状态
            boolean isConnected = sseConnectionService.isUserConnected(userId);
            int totalConnections = sseConnectionService.getConnectionCount();

            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("isConnected", isConnected);
            result.put("totalConnections", totalConnections);

            log.info("调试连接状态 - 用户ID: {}, 是否连接: {}, 总连接数: {}", userId, isConnected, totalConnections);

            return success(result);

        } catch (Exception e) {
            log.error("检查连接状态失败", e);
            return error("检查连接状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理超时通知
     */
    @PostMapping("/cleanup-timeout")
    @Log(title = "清理超时通知", businessType = BusinessType.OTHER)
    public AjaxResult cleanupTimeoutNotifications() {
        try {
            Long userId = SecurityUtils.getUserId();

            // 将用户的所有SENT状态通知标记为DELIVERED
            int count = notificationSendService.markUserNotificationsAsDelivered(userId);

            log.info("清理用户 {} 的超时通知，共处理 {} 条", userId, count);
            return success("成功清理 " + count + " 条超时通知");

        } catch (Exception e) {
            log.error("清理超时通知失败", e);
            return error("清理超时通知失败: " + e.getMessage());
        }
    }

    /**
     * 广播通知给所有在线用户
     */
    @PostMapping("/broadcast")
    @Log(title = "广播通知", businessType = BusinessType.OTHER)
    public AjaxResult broadcastNotification(@RequestBody Map<String, Object> params) {
        String title = (String) params.get("title");
        String content = (String) params.get("content");

        ConsultationNotificationMessage message = ConsultationNotificationMessage
            .createSystemNotification(title, content, null);

        int successCount = sseConnectionService.broadcastNotification(message);

        return success("广播通知完成，成功发送给 " + successCount + " 个用户");
    }
}
